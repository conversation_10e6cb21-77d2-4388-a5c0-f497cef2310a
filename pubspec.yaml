name: money_mouthy_two
description: "A new Flutter project."
publish_to: "none"
version: 1.0.0+1

environment:
  sdk: ">=3.3.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  firebase_core: ^3.15.1
  firebase_auth: ^5.6.2
  cloud_firestore: 5.6.11
  shared_preferences: ^2.5.3
  flutter_secure_storage: ^9.2.4
  # Payment Gateway
  flutter_stripe: ^10.2.0
  flutter_stripe_web: ^5.1.0

  http: ^1.4.0
  web: ^1.1.1
  js: ^0.6.5
  # Wallet & Transactions
  sqflite: ^2.4.1
  path: ^1.9.0
  # intl: ^0.20.2
  firebase_storage: ^12.4.9
  image_picker: ^1.1.2
  font_awesome_flutter:
  share_plus: ^11.0.0
  url_launcher: ^6.3.1
  app_links: ^6.4.0
  universal_html: ^2.2.4
  video_player: ^2.9.5
  video_thumbnail: ^0.5.6
  path_provider: ^2.1.5
  youtube_player_iframe: ^5.2.1
  image_network: ^2.6.0
  get: ^4.7.2
  cloud_functions: 5.6.1
  stripe_js: ^3.4.0
  easy_localization: ^3.0.7+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/money_mouth.png"
  min_sdk_android: 21 # android min sdk min: 21

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/translations/
