import 'dart:ui';

import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../services/category_preference_service.dart';
import '../widgets/home/<USER>';
import 'post_controller.dart';

/// GetX Controller for managing category state across the entire app
class CategoryController extends GetxController {
  static CategoryController get instance => Get.find();

  // Reactive variables - now name-based instead of index-based
  final _selectedCategoryName = Categories.defaultCategory.obs;
  final _isLoading = false.obs;

  // Getters for reactive access
  String get selectedCategoryName => _selectedCategoryName.value;
  bool get isLoading => _isLoading.value;

  // Computed getter for index (for backward compatibility)
  int get selectedCategoryIndex =>
      Categories.getIndexByName(_selectedCategoryName.value);

  // Reactive getters for UI binding
  RxString get selectedCategoryNameRx => _selectedCategoryName;
  RxBool get isLoadingRx => _isLoading;

  // Computed reactive getter for index
  int get selectedCategoryIndexRx =>
      Categories.getIndexByName(_selectedCategoryName.value);

  // Get current category data
  CategoryData get selectedCategory => Categories.all[selectedCategoryIndex];

  @override
  void onInit() {
    super.onInit();
    // Don't auto-load - wait for explicit initialization after authentication
    // Set default category for now
    _selectedCategoryName.value = Categories.defaultCategory;
    debugPrint(
        'CategoryController: Created with default category - waiting for authentication');
  }

  /// Load user's selected category from Firebase with authentication check
  Future<void> _loadUserCategory() async {
    try {
      _isLoading.value = true;

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        // Email verification check removed
        debugPrint(
            'CategoryController: User not authenticated, using default category');
        _selectedCategoryName.value = Categories.defaultCategory;
        return;
      }

      debugPrint(
          'CategoryController: Loading category for authenticated user: ${user.uid}');

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data();
        final savedCategory = userData?['selectedCategory'];

        if (savedCategory != null &&
            Categories.isValidCategory(savedCategory)) {
          _selectedCategoryName.value = savedCategory;
          debugPrint(
            'CategoryController: Loaded saved category "$savedCategory"',
          );
        } else {
          // Invalid category, use default
          _selectedCategoryName.value = Categories.defaultCategory;
          debugPrint(
            'CategoryController: Invalid saved category "$savedCategory", using default',
          );
        }
      } else {
        // No user document, use default
        _selectedCategoryName.value = Categories.defaultCategory;
        debugPrint(
            'CategoryController: No user document found, using default category');
      }
    } catch (e) {
      debugPrint('CategoryController: Error loading user category: $e');
      // Fallback to default category
      _selectedCategoryName.value = Categories.defaultCategory;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update selected category by index and sync to Firebase
  Future<void> updateCategory(int categoryIndex) async {
    final categoryName = Categories.getNameByIndex(categoryIndex);
    if (categoryName == null) {
      debugPrint('CategoryController: Invalid category index: $categoryIndex');
      return;
    }

    await updateCategoryByName(categoryName);
  }

  /// Update selected category by name and sync to Firebase
  Future<void> updateCategoryByName(String categoryName) async {
    if (!Categories.isValidCategory(categoryName)) {
      debugPrint('CategoryController: Invalid category name: $categoryName');
      return;
    }

    if (categoryName == _selectedCategoryName.value) {
      debugPrint(
          'CategoryController: Category "$categoryName" already selected');
      return;
    }

    try {
      _isLoading.value = true;

      // Update reactive variable immediately for UI responsiveness
      _selectedCategoryName.value = categoryName;

      debugPrint('CategoryController: Updated category to "$categoryName"');

      // Save to Firebase and SharedPreferences
      await _saveCategoryToFirebase(categoryName);
      await CategoryPreferenceService.saveLastSelectedCategory(categoryName);

      // Trigger cache invalidation
      _notifyCacheInvalidation();
    } catch (e) {
      debugPrint('CategoryController: Error updating category: $e');
      // Revert on error
      await _loadUserCategory();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Cache invalidation notification for other controllers
  void _notifyCacheInvalidation() {
    // Notify PostController to invalidate cache if it exists
    try {
      if (Get.isRegistered<PostController>()) {
        final postController = Get.find<PostController>();
        // Force update to trigger cache invalidation
        postController.update();
      }
    } catch (e) {
      debugPrint('CategoryController: Error notifying cache invalidation: $e');
    }
  }

  /// Save category to Firebase
  Future<void> _saveCategoryToFirebase(String categoryName) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .update({
          'selectedCategory': categoryName,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        debugPrint(
          'CategoryController: Saved category "$categoryName" to Firebase',
        );
      }
    } catch (e) {
      debugPrint('CategoryController: Error saving category to Firebase: $e');
      rethrow;
    }
  }

  /// Refresh category from Firebase (useful for syncing across devices)
  Future<void> refreshCategory() async {
    await _loadUserCategory();
  }

  /// Get category color by index
  Color getCategoryColor(int index) {
    if (index >= 0 && index < Categories.all.length) {
      return Categories.all[index].color;
    }
    return Categories.all[0].color; // Default to first category color
  }

  /// Get category name by index
  String getCategoryName(int index) {
    if (index >= 0 && index < Categories.all.length) {
      return Categories.all[index].name;
    }
    return Categories.all[0].name; // Default to first category name
  }

  /// Check if a category is currently selected by index
  bool isCategorySelected(int index) {
    return index == selectedCategoryIndex;
  }

  /// Check if a category is currently selected by name
  bool isCategorySelectedByName(String categoryName) {
    return categoryName == _selectedCategoryName.value;
  }

  /// Get all available categories
  List<CategoryData> get allCategories => Categories.all;

  /// Reset to default category (News)
  Future<void> resetToDefault() async {
    await updateCategoryByName(Categories.defaultCategory);
  }

  /// Initialize category controller after authentication
  Future<void> initializeWithAuth() async {
    await _loadUserCategory();
  }
}
