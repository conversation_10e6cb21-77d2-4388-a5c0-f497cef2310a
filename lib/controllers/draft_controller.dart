import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../models/draft_model.dart';
import '../services/draft_service.dart';

/// Controller for managing post drafts with reactive state management
class Draft<PERSON>ontroller extends GetxController {
  // Observable lists
  final _drafts = <DraftModel>[].obs;
  final _isLoading = false.obs;
  final _autoDraft = Rxn<DraftModel>();

  // Auto-save timer
  Timer? _autoSaveTimer;
  static const Duration _autoSaveInterval = Duration(seconds: 3);

  // Current draft being edited
  DraftModel? _currentDraft;

  // Getters
  List<DraftModel> get drafts => _drafts;
  bool get isLoading => _isLoading.value;
  DraftModel? get autoDraft => _autoDraft.value;
  bool get hasDrafts => _drafts.isNotEmpty;
  int get draftsCount => _drafts.length;

  @override
  void onInit() {
    super.onInit();
    loadDrafts();
    loadAutoDraft();
  }

  @override
  void onClose() {
    _autoSaveTimer?.cancel();
    super.onClose();
  }

  /// Load all drafts from storage
  Future<void> loadDrafts() async {
    try {
      _isLoading.value = true;
      final loadedDrafts = await DraftService.getAllDrafts();
      _drafts.assignAll(loadedDrafts);

      if (kDebugMode) {
        print('DraftController: Loaded ${loadedDrafts.length} drafts');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DraftController: Error loading drafts: $e');
      }
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load auto-draft from storage
  Future<void> loadAutoDraft() async {
    try {
      final autoDraftData = await DraftService.getAutoDraft();
      _autoDraft.value = autoDraftData;

      if (kDebugMode && autoDraftData != null) {
        print('DraftController: Loaded auto-draft ${autoDraftData.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DraftController: Error loading auto-draft: $e');
      }
    }
  }

  /// Save a draft manually
  Future<bool> saveDraft({
    String? title,
    required String content,
    required double price,
    required String category,
    List<String>? tags,
    bool isPublic = true,
    bool allowComments = true,
    String? linkUrl,
    List<XFile>? images,
    List<XFile>? videos,
    bool hasPoll = false,
  }) async {
    try {
      // Convert XFile to paths
      final imagePaths = images?.map((file) => file.path).toList() ?? [];
      final videoPaths = videos?.map((file) => file.path).toList() ?? [];

      final draft = DraftModel.create(
        title: title,
        content: content,
        price: price,
        category: category,
        tags: tags,
        isPublic: isPublic,
        allowComments: allowComments,
        linkUrl: linkUrl,
        imagePaths: imagePaths,
        videoPaths: videoPaths,
        hasPoll: hasPoll,
      );

      final success = await DraftService.saveDraft(draft);

      if (success) {
        // Add to local list if not already present
        final existingIndex = _drafts.indexWhere((d) => d.id == draft.id);
        if (existingIndex >= 0) {
          _drafts[existingIndex] = draft;
        } else {
          _drafts.insert(0, draft);
        }

        if (kDebugMode) {
          print('DraftController: Saved draft ${draft.id}');
        }
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('DraftController: Error saving draft: $e');
      }
      return false;
    }
  }

  /// Update an existing draft
  Future<bool> updateDraft(
    String draftId, {
    String? title,
    String? content,
    double? price,
    String? category,
    List<String>? tags,
    bool? isPublic,
    bool? allowComments,
    String? linkUrl,
    List<XFile>? images,
    List<XFile>? videos,
  }) async {
    try {
      final existingDraft = _drafts.firstWhere(
        (d) => d.id == draftId,
        orElse: () => throw StateError('Draft not found'),
      );

      // Convert XFile to paths
      final imagePaths = images?.map((file) => file.path).toList();
      final videoPaths = videos?.map((file) => file.path).toList();

      final updatedDraft = existingDraft.updated(
        title: title,
        content: content,
        price: price,
        category: category,
        tags: tags,
        isPublic: isPublic,
        allowComments: allowComments,
        linkUrl: linkUrl,
        imagePaths: imagePaths,
        videoPaths: videoPaths,
      );

      final success = await DraftService.saveDraft(updatedDraft);

      if (success) {
        final index = _drafts.indexWhere((d) => d.id == draftId);
        if (index >= 0) {
          _drafts[index] = updatedDraft;
        }

        if (kDebugMode) {
          print('DraftController: Updated draft $draftId');
        }
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('DraftController: Error updating draft $draftId: $e');
      }
      return false;
    }
  }

  /// Delete a specific draft
  Future<bool> deleteDraft(String draftId) async {
    try {
      final success = await DraftService.deleteDraft(draftId);

      if (success) {
        _drafts.removeWhere((draft) => draft.id == draftId);

        if (kDebugMode) {
          print('DraftController: Deleted draft $draftId');
        }
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('DraftController: Error deleting draft $draftId: $e');
      }
      return false;
    }
  }

  /// Delete all drafts
  Future<bool> deleteAllDrafts() async {
    try {
      final success = await DraftService.deleteAllDrafts();

      if (success) {
        _drafts.clear();

        if (kDebugMode) {
          print('DraftController: Deleted all drafts');
        }
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('DraftController: Error deleting all drafts: $e');
      }
      return false;
    }
  }

  /// Start auto-saving current draft
  void startAutoSave({
    String? title,
    required String content,
    required double price,
    required String category,
    List<String>? tags,
    bool isPublic = true,
    bool allowComments = true,
    String? linkUrl,
    List<XFile>? images,
    List<XFile>? videos,
    bool hasPoll = false,
  }) {
    _autoSaveTimer?.cancel();

    _autoSaveTimer = Timer(_autoSaveInterval, () async {
      await _saveAutoDraft(
        title: title,
        content: content,
        price: price,
        category: category,
        tags: tags,
        isPublic: isPublic,
        allowComments: allowComments,
        linkUrl: linkUrl,
        images: images,
        videos: videos,
        hasPoll: hasPoll,
      );
    });
  }

  /// Save auto-draft (internal method)
  Future<void> _saveAutoDraft({
    String? title,
    required String content,
    required double price,
    required String category,
    List<String>? tags,
    bool isPublic = true,
    bool allowComments = true,
    String? linkUrl,
    List<XFile>? images,
    List<XFile>? videos,
    bool hasPoll = false,
  }) async {
    try {
      // Don't save empty drafts
      if (content.trim().isEmpty &&
          (title?.trim().isEmpty ?? true) &&
          (images?.isEmpty ?? true) &&
          (videos?.isEmpty ?? true) &&
          (linkUrl?.trim().isEmpty ?? true)) {
        return;
      }

      // Convert XFile to paths
      final imagePaths = images?.map((file) => file.path).toList() ?? [];
      final videoPaths = videos?.map((file) => file.path).toList() ?? [];

      final draft = DraftModel.create(
        title: title,
        content: content,
        price: price,
        category: category,
        tags: tags,
        isPublic: isPublic,
        allowComments: allowComments,
        linkUrl: linkUrl,
        imagePaths: imagePaths,
        videoPaths: videoPaths,
        hasPoll: hasPoll,
      );

      await DraftService.saveAutoDraft(draft);
      _autoDraft.value = draft;

      if (kDebugMode) {
        print('DraftController: Auto-saved draft');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DraftController: Error auto-saving draft: $e');
      }
    }
  }

  /// Clear auto-draft
  Future<void> clearAutoDraft() async {
    try {
      await DraftService.clearAutoDraft();
      _autoDraft.value = null;

      if (kDebugMode) {
        print('DraftController: Cleared auto-draft');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DraftController: Error clearing auto-draft: $e');
      }
    }
  }

  /// Stop auto-save timer
  void stopAutoSave() {
    _autoSaveTimer?.cancel();
  }

  /// Get a specific draft
  DraftModel? getDraft(String draftId) {
    try {
      return _drafts.firstWhere((draft) => draft.id == draftId);
    } catch (e) {
      return null;
    }
  }

  /// Check if storage is near limit
  Future<bool> isNearStorageLimit() async {
    return await DraftService.isNearLimit();
  }

  /// Clean up invalid drafts
  Future<void> cleanupInvalidDrafts() async {
    await DraftService.cleanupInvalidDrafts();
    await loadDrafts(); // Reload after cleanup
  }
}
