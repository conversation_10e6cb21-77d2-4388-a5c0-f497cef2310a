import 'package:flutter/foundation.dart';
import 'transaction_model.dart';

enum WalletStatus { uninitialized, initializing, ready, error, syncing }

@immutable
class WalletState {
  final double balance;
  final List<TransactionModel> transactions;
  final WalletStatus status;
  final String? errorMessage;
  final DateTime lastUpdated;
  final bool isLoading;
  final double totalEarnings;
  final double totalSpent;

  const WalletState({
    required this.balance,
    required this.transactions,
    required this.status,
    this.errorMessage,
    required this.lastUpdated,
    required this.isLoading,
    required this.totalEarnings,
    required this.totalSpent,
  });

  factory WalletState.initial() {
    return WalletState(
      balance: 0.0,
      transactions: const [],
      status: WalletStatus.uninitialized,
      errorMessage: null,
      lastUpdated: DateTime.now(),
      isLoading: false,
      totalEarnings: 0.0,
      totalSpent: 0.0,
    );
  }

  factory WalletState.loading() {
    return WalletState(
      balance: 0.0,
      transactions: const [],
      status: WalletStatus.initializing,
      errorMessage: null,
      lastUpdated: DateTime.now(),
      isLoading: true,
      totalEarnings: 0.0,
      totalSpent: 0.0,
    );
  }

  factory WalletState.error(String message) {
    return WalletState(
      balance: 0.0,
      transactions: const [],
      status: WalletStatus.error,
      errorMessage: message,
      lastUpdated: DateTime.now(),
      isLoading: false,
      totalEarnings: 0.0,
      totalSpent: 0.0,
    );
  }

  WalletState copyWith({
    double? balance,
    List<TransactionModel>? transactions,
    WalletStatus? status,
    String? errorMessage,
    DateTime? lastUpdated,
    bool? isLoading,
    double? totalEarnings,
    double? totalSpent,
  }) {
    return WalletState(
      balance: balance ?? this.balance,
      transactions: transactions ?? this.transactions,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isLoading: isLoading ?? this.isLoading,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      totalSpent: totalSpent ?? this.totalSpent,
    );
  }

  WalletState clearError() {
    return copyWith(status: WalletStatus.ready, errorMessage: null);
  }

  WalletState setLoading(bool loading) {
    return copyWith(
      isLoading: loading,
      status: loading ? WalletStatus.syncing : WalletStatus.ready,
    );
  }

  bool get isReady => status == WalletStatus.ready;
  bool get hasError => status == WalletStatus.error;
  bool get isInitialized => status != WalletStatus.uninitialized;

  List<TransactionModel> get creditTransactions =>
      transactions.where((t) => t.isCredit).toList();

  List<TransactionModel> get debitTransactions =>
      transactions.where((t) => t.isDebit).toList();

  List<TransactionModel> get pendingTransactions =>
      transactions.where((t) => t.isPending).toList();

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WalletState &&
        other.balance == balance &&
        listEquals(other.transactions, transactions) &&
        other.status == status &&
        other.errorMessage == errorMessage &&
        other.isLoading == isLoading &&
        other.totalEarnings == totalEarnings &&
        other.totalSpent == totalSpent;
  }

  @override
  int get hashCode {
    return Object.hash(
      balance,
      transactions,
      status,
      errorMessage,
      isLoading,
      totalEarnings,
      totalSpent,
    );
  }

  @override
  String toString() {
    return 'WalletState(balance: $balance, status: $status, transactions: ${transactions.length}, isLoading: $isLoading)';
  }
}
