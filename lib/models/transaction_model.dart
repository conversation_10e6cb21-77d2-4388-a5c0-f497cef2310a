import 'package:cloud_firestore/cloud_firestore.dart';

enum TransactionType {
  credit,
  debit,
  withdrawal,
  postPayment,
  refund,
}

enum TransactionStatus {
  pending,
  completed,
  failed,
  cancelled,
}

class TransactionModel {
  final String id;
  final TransactionType type;
  final double amount;
  final String description;
  final DateTime timestamp;
  final TransactionStatus status;
  final String? postId;
  final String? paymentMethodId;
  final String? externalTransactionId;
  final Map<String, dynamic>? metadata;

  const TransactionModel({
    required this.id,
    required this.type,
    required this.amount,
    required this.description,
    required this.timestamp,
    required this.status,
    this.postId,
    this.paymentMethodId,
    this.externalTransactionId,
    this.metadata,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'amount': amount,
      'description': description,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'status': status.name,
      'postId': postId,
      'paymentMethodId': paymentMethodId,
      'externalTransactionId': externalTransactionId,
      'metadata': metadata,
    };
  }

  factory TransactionModel.fromMap(String id, Map<String, dynamic> map) {
    // Handle timestamp - can be int (milliseconds) or Firestore Timestamp
    DateTime timestamp;
    final timestampValue = map['timestamp'];
    if (timestampValue is int) {
      timestamp = DateTime.fromMillisecondsSinceEpoch(timestampValue);
    } else if (timestampValue is Timestamp) {
      timestamp = timestampValue.toDate();
    } else {
      timestamp = DateTime.now(); // Fallback
    }

    return TransactionModel(
      id: id,
      type: TransactionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TransactionType.debit,
      ),
      amount: (map['amount'] as num).toDouble(),
      description: map['description'] ?? '',
      timestamp: timestamp,
      status: TransactionStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => TransactionStatus.completed,
      ),
      postId: map['postId'],
      paymentMethodId: map['paymentMethodId'],
      externalTransactionId: map['externalTransactionId'],
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  factory TransactionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return TransactionModel.fromMap(doc.id, data);
  }

  TransactionModel copyWith({
    String? id,
    TransactionType? type,
    double? amount,
    String? description,
    DateTime? timestamp,
    TransactionStatus? status,
    String? postId,
    String? paymentMethodId,
    String? externalTransactionId,
    Map<String, dynamic>? metadata,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      postId: postId ?? this.postId,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      externalTransactionId:
          externalTransactionId ?? this.externalTransactionId,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isCredit =>
      type == TransactionType.credit || type == TransactionType.refund;
  bool get isDebit =>
      type == TransactionType.debit ||
      type == TransactionType.withdrawal ||
      type == TransactionType.postPayment;
  bool get isPending => status == TransactionStatus.pending;
  bool get isCompleted => status == TransactionStatus.completed;
  bool get isFailed => status == TransactionStatus.failed;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TransactionModel(id: $id, type: $type, amount: $amount, status: $status)';
  }
}
