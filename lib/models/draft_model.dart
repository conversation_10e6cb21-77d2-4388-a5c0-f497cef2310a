import 'dart:convert';

/// Model for storing post drafts locally
class DraftModel {
  final String id;
  final String? title;
  final String content;
  final double price;
  final String category;
  final List<String> tags;
  final bool isPublic;
  final bool allowComments;
  final String? linkUrl;
  final List<String> imagePaths; // Local file paths for images
  final List<String> videoPaths; // Local file paths for videos
  final bool hasPoll; // Whether this draft has a poll
  final DateTime createdAt;
  final DateTime updatedAt;

  const DraftModel({
    required this.id,
    this.title,
    required this.content,
    required this.price,
    required this.category,
    this.tags = const [],
    this.isPublic = true,
    this.allowComments = true,
    this.linkUrl,
    this.imagePaths = const [],
    this.videoPaths = const [],
    this.hasPoll = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create a new draft with current timestamp
  factory DraftModel.create({
    String? title,
    required String content,
    required double price,
    required String category,
    List<String>? tags,
    bool isPublic = true,
    bool allowComments = true,
    String? linkUrl,
    List<String>? imagePaths,
    List<String>? videoPaths,
    bool hasPoll = false,
  }) {
    final now = DateTime.now();
    return DraftModel(
      id: now.millisecondsSinceEpoch.toString(),
      title: title,
      content: content,
      price: price,
      category: category,
      tags: tags ?? [],
      isPublic: isPublic,
      allowComments: allowComments,
      linkUrl: linkUrl,
      imagePaths: imagePaths ?? [],
      videoPaths: videoPaths ?? [],
      hasPoll: hasPoll,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'price': price,
      'category': category,
      'tags': tags,
      'isPublic': isPublic,
      'allowComments': allowComments,
      'linkUrl': linkUrl,
      'imagePaths': imagePaths,
      'videoPaths': videoPaths,
      'hasPoll': hasPoll,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  /// Create from JSON
  factory DraftModel.fromJson(Map<String, dynamic> json) {
    return DraftModel(
      id: json['id'] as String,
      title: json['title'] as String?,
      content: json['content'] as String,
      price: (json['price'] as num).toDouble(),
      category: json['category'] as String,
      tags: List<String>.from(json['tags'] as List? ?? []),
      isPublic: json['isPublic'] as bool? ?? true,
      allowComments: json['allowComments'] as bool? ?? true,
      linkUrl: json['linkUrl'] as String?,
      imagePaths: List<String>.from(json['imagePaths'] as List? ?? []),
      videoPaths: List<String>.from(json['videoPaths'] as List? ?? []),
      hasPoll: json['hasPoll'] as bool? ?? false,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt'] as int),
    );
  }

  /// Create a copy with updated fields
  DraftModel copyWith({
    String? id,
    String? title,
    String? content,
    double? price,
    String? category,
    List<String>? tags,
    bool? isPublic,
    bool? allowComments,
    String? linkUrl,
    List<String>? imagePaths,
    List<String>? videoPaths,
    bool? hasPoll,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DraftModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      price: price ?? this.price,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      isPublic: isPublic ?? this.isPublic,
      allowComments: allowComments ?? this.allowComments,
      linkUrl: linkUrl ?? this.linkUrl,
      imagePaths: imagePaths ?? this.imagePaths,
      videoPaths: videoPaths ?? this.videoPaths,
      hasPoll: hasPoll ?? this.hasPoll,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Update with current timestamp
  DraftModel updated({
    String? title,
    String? content,
    double? price,
    String? category,
    List<String>? tags,
    bool? isPublic,
    bool? allowComments,
    String? linkUrl,
    List<String>? imagePaths,
    List<String>? videoPaths,
  }) {
    return copyWith(
      title: title,
      content: content,
      price: price,
      category: category,
      tags: tags,
      isPublic: isPublic,
      allowComments: allowComments,
      linkUrl: linkUrl,
      imagePaths: imagePaths,
      videoPaths: videoPaths,
      updatedAt: DateTime.now(),
    );
  }

  /// Check if draft is empty (no meaningful content)
  bool get isEmpty {
    return content.trim().isEmpty &&
        (title?.trim().isEmpty ?? true) &&
        imagePaths.isEmpty &&
        videoPaths.isEmpty &&
        (linkUrl?.trim().isEmpty ?? true);
  }

  /// Check if draft has any media content
  bool get hasMedia {
    return imagePaths.isNotEmpty || videoPaths.isNotEmpty;
  }

  /// Get a preview of the content (first 50 characters)
  String get contentPreview {
    if (content.trim().isEmpty) return 'No content';
    final preview = content.trim();
    return preview.length > 50 ? '${preview.substring(0, 50)}...' : preview;
  }

  /// Get formatted creation time
  String get formattedCreatedAt {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  String toString() {
    return 'DraftModel(id: $id, content: ${contentPreview}, category: $category, hasMedia: $hasMedia)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DraftModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
