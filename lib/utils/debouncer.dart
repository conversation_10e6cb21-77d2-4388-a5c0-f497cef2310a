import 'dart:async';

/// A utility class for debouncing function calls
/// Prevents excessive API calls from rapid user interactions
class Debouncer {
  final Duration delay;
  Timer? _timer;

  Debouncer({required this.delay});

  /// Debounce a function call
  void call(void Function() action) {
    _timer?.cancel();
    _timer = Timer(delay, action);
  }

  /// Cancel any pending debounced calls
  void cancel() {
    _timer?.cancel();
  }

  /// Dispose of the debouncer
  void dispose() {
    _timer?.cancel();
  }
}

/// A utility class for throttling function calls
/// Ensures a function is called at most once per time period
class Throttler {
  final Duration duration;
  DateTime? _lastExecution;
  Timer? _timer;

  Throttler({required this.duration});

  /// Throttle a function call
  void call(void Function() action) {
    final now = DateTime.now();
    
    if (_lastExecution == null || 
        now.difference(_lastExecution!) >= duration) {
      // Execute immediately if enough time has passed
      _lastExecution = now;
      action();
    } else {
      // Schedule for later if not enough time has passed
      _timer?.cancel();
      final timeToWait = duration - now.difference(_lastExecution!);
      _timer = Timer(timeToWait, () {
        _lastExecution = DateTime.now();
        action();
      });
    }
  }

  /// Cancel any pending throttled calls
  void cancel() {
    _timer?.cancel();
  }

  /// Dispose of the throttler
  void dispose() {
    _timer?.cancel();
  }
}

/// A utility class for rate limiting API calls
/// Prevents too many requests in a given time window
class RateLimiter {
  final int maxRequests;
  final Duration timeWindow;
  final List<DateTime> _requestTimes = [];

  RateLimiter({
    required this.maxRequests,
    required this.timeWindow,
  });

  /// Check if a request can be made
  bool canMakeRequest() {
    final now = DateTime.now();
    
    // Remove old requests outside the time window
    _requestTimes.removeWhere(
      (time) => now.difference(time) > timeWindow,
    );
    
    return _requestTimes.length < maxRequests;
  }

  /// Record a request
  void recordRequest() {
    _requestTimes.add(DateTime.now());
  }

  /// Try to make a request, returns true if allowed
  bool tryRequest() {
    if (canMakeRequest()) {
      recordRequest();
      return true;
    }
    return false;
  }

  /// Get time until next request is allowed
  Duration? getTimeUntilNextRequest() {
    if (canMakeRequest()) return null;
    
    final oldestRequest = _requestTimes.first;
    final timeUntilExpiry = timeWindow - DateTime.now().difference(oldestRequest);
    
    return timeUntilExpiry.isNegative ? null : timeUntilExpiry;
  }

  /// Clear all recorded requests
  void reset() {
    _requestTimes.clear();
  }
}

/// Mixin for adding debouncing capabilities to widgets
mixin DebounceMixin {
  final Map<String, Debouncer> _debouncers = {};

  /// Get or create a debouncer with a specific key
  Debouncer getDebouncer(String key, {Duration delay = const Duration(milliseconds: 500)}) {
    return _debouncers.putIfAbsent(key, () => Debouncer(delay: delay));
  }

  /// Debounce a function call with a specific key
  void debounce(String key, void Function() action, {Duration delay = const Duration(milliseconds: 500)}) {
    getDebouncer(key, delay: delay).call(action);
  }

  /// Dispose all debouncers
  void disposeAllDebouncers() {
    for (final debouncer in _debouncers.values) {
      debouncer.dispose();
    }
    _debouncers.clear();
  }
}

/// Mixin for adding throttling capabilities to widgets
mixin ThrottleMixin {
  final Map<String, Throttler> _throttlers = {};

  /// Get or create a throttler with a specific key
  Throttler getThrottler(String key, {Duration duration = const Duration(seconds: 1)}) {
    return _throttlers.putIfAbsent(key, () => Throttler(duration: duration));
  }

  /// Throttle a function call with a specific key
  void throttle(String key, void Function() action, {Duration duration = const Duration(seconds: 1)}) {
    getThrottler(key, duration: duration).call(action);
  }

  /// Dispose all throttlers
  void disposeAllThrottlers() {
    for (final throttler in _throttlers.values) {
      throttler.dispose();
    }
    _throttlers.clear();
  }
}
