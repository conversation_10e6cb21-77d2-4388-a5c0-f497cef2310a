import 'dart:async';
import 'package:flutter/foundation.dart';

class ProfileUpdateService {
  static final ProfileUpdateService _instance = ProfileUpdateService._internal();
  factory ProfileUpdateService() => _instance;
  ProfileUpdateService._internal();

  // Stream controllers for different types of profile updates
  final StreamController<String> _profileImageUpdateController = StreamController<String>.broadcast();
  final StreamController<Map<String, dynamic>> _profileDataUpdateController = StreamController<Map<String, dynamic>>.broadcast();

  // Streams that other widgets can listen to
  Stream<String> get profileImageUpdateStream => _profileImageUpdateController.stream;
  Stream<Map<String, dynamic>> get profileDataUpdateStream => _profileDataUpdateController.stream;

  /// Notify all listeners that a user's profile image has been updated
  void notifyProfileImageUpdate(String userId, String newImageUrl) {
    debugPrint('ProfileUpdateService: Notifying profile image update for user $userId');
    _profileImageUpdateController.add(userId);
  }

  /// Notify all listeners that a user's profile data has been updated
  void notifyProfileDataUpdate(String userId, Map<String, dynamic> updatedData) {
    debugPrint('ProfileUpdateService: Notifying profile data update for user $userId');
    final updateData = {
      'userId': userId,
      ...updatedData,
    };
    _profileDataUpdateController.add(updateData);
  }

  /// Dispose of resources
  void dispose() {
    _profileImageUpdateController.close();
    _profileDataUpdateController.close();
  }
}
