import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/post_service.dart';
import '../screens/post_detail_screen.dart';
import '../screens/login.dart';
import '../screens/main_navigation_screen.dart';
import '../controllers/post_controller.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Web-specific deep link handler
class WebDeepLinkHandler {
  static final WebDeepLinkHandler _instance = WebDeepLinkHandler._internal();
  factory WebDeepLinkHandler() => _instance;
  WebDeepLinkHandler._internal();

  final PostService _postService = PostService();
  String? _pendingPostId;

  /// Initialize web deep link handling - store the link for later processing
  void initialize() {
    if (!kIsWeb) return;

    // Store the initial URL to handle after screens are loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _storeInitialUrl();
    });
  }

  /// Store the initial URL for processing after screens are loaded
  void _storeInitialUrl() {
    try {
      // Get current URL from browser
      final currentUrl = Uri.base.toString();
      debugPrint('Storing initial URL: $currentUrl');

      final postId = _extractPostIdFromUrl(currentUrl);
      if (postId != null && postId.isNotEmpty) {
        debugPrint('Found post ID in initial URL: $postId');
        // Store for later processing
        _pendingPostId = postId;
        debugPrint('Stored pending post ID: $postId');
      }
    } catch (e) {
      debugPrint('Error storing initial URL: $e');
    }
  }

  /// Extract post ID from various URL formats
  String? _extractPostIdFromUrl(String url) {
    try {
      final uri = Uri.parse(url);

      // Check for /#/post/POST_ID format (hash routing)
      if (uri.fragment.isNotEmpty) {
        final fragment = uri.fragment;
        if (fragment.startsWith('/post/')) {
          return fragment.replaceFirst('/post/', '');
        }
      }

      // Check for /post/POST_ID format (direct routing)
      if (uri.pathSegments.length >= 2 && uri.pathSegments[0] == 'post') {
        return uri.pathSegments[1];
      }

      // Check for query parameters
      final postId =
          uri.queryParameters['postId'] ?? uri.queryParameters['post_id'];
      if (postId != null && postId.isNotEmpty) {
        return postId;
      }

      return null;
    } catch (e) {
      debugPrint('Error extracting post ID from URL: $e');
      return null;
    }
  }

  /// Navigate to post detail screen
  Future<void> _navigateToPost(String postId) async {
    try {
      debugPrint('Attempting to navigate to post: $postId');

      // Wait for the app to be ready
      await _waitForAppReady();

      // Initialize PostService
      await _postService.initialize();

      // Find the post
      final posts = _postService.getAllPosts();
      final post = posts.firstWhereOrNull((p) => p.id == postId);

      if (post != null) {
        debugPrint('Post found, navigating to detail screen');

        // Ensure we're on the main navigation screen first
        if (Get.currentRoute != '/home') {
          Get.offAllNamed('/home');
          // Wait a bit for the navigation to complete
          await Future.delayed(const Duration(milliseconds: 300));
        }

        // Navigate to post detail screen
        Get.to(
          () => PostDetailScreen(post: post),
          transition: Transition.cupertino,
          duration: const Duration(milliseconds: 300),
        );
      } else {
        debugPrint('Post not found: $postId');
        _showPostNotFoundDialog();
      }
    } catch (e) {
      debugPrint('Error navigating to post: $e');
      _showPostNotFoundDialog();
    }
  }

  /// Wait for the app to be ready for navigation
  Future<void> _waitForAppReady() async {
    // Wait for GetX to be ready
    int attempts = 0;
    while (!Get.isRegistered<PostController>() && attempts < 50) {
      await Future.delayed(const Duration(milliseconds: 100));
      attempts++;
    }
    debugPrint('App ready after $attempts attempts');
  }

  /// Show post not found dialog
  void _showPostNotFoundDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Post Not Found'),
        content: const Text(
          'The post you\'re looking for could not be found. It may have been deleted or is no longer available.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // Close dialog
              Get.offAllNamed('/home');
            },
            child: const Text('Go to Home'),
          ),
        ],
      ),
    );
  }

  /// Handle navigation after screens are loaded (called from MainNavigationScreen)
  Future<void> handlePostLoginNavigation() async {
    if (_pendingPostId != null) {
      final postId = _pendingPostId!;
      _pendingPostId = null; // Clear pending post ID

      debugPrint('Handling navigation to post after screens loaded: $postId');

      // Wait for screens to be fully loaded
      await Future.delayed(const Duration(milliseconds: 1000));

      await _navigateToPost(postId);
    }
  }

  /// Check if there's a pending post navigation
  bool get hasPendingNavigation => _pendingPostId != null;

  /// Get pending post ID
  String? get pendingPostId => _pendingPostId;

  /// Clear pending navigation
  void clearPendingNavigation() {
    _pendingPostId = null;
  }

  /// Generate web-friendly URL for sharing
  String generateWebUrl(String postId) {
    if (kIsWeb) {
      final currentUri = Uri.base;
      return '${currentUri.scheme}://${currentUri.host}${currentUri.port != 80 && currentUri.port != 443 ? ':${currentUri.port}' : ''}/#/post/$postId';
    }
    return 'https://moneymouthy.com/post/$postId';
  }
}
