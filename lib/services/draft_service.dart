import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/draft_model.dart';

/// Service for managing post drafts locally using SharedPreferences
class DraftService {
  static const String _draftsKey = 'post_drafts';
  static const String _autoDraftKey = 'auto_draft';
  static const int _maxDrafts = 50; // Maximum number of drafts to keep

  /// Save a draft
  static Future<bool> saveDraft(DraftModel draft) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final drafts = await getAllDrafts();
      
      // Remove existing draft with same ID if it exists
      drafts.removeWhere((d) => d.id == draft.id);
      
      // Add the new/updated draft at the beginning
      drafts.insert(0, draft);
      
      // Limit the number of drafts
      if (drafts.length > _maxDrafts) {
        drafts.removeRange(_maxDrafts, drafts.length);
      }
      
      // Convert to JSON and save
      final draftsJson = drafts.map((d) => d.toJson()).toList();
      final success = await prefs.setString(_draftsKey, jsonEncode(draftsJson));
      
      if (kDebugMode) {
        print('DraftService: Saved draft ${draft.id} - Success: $success');
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('DraftService: Error saving draft: $e');
      }
      return false;
    }
  }

  /// Get all drafts sorted by updated time (newest first)
  static Future<List<DraftModel>> getAllDrafts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final draftsString = prefs.getString(_draftsKey);
      
      if (draftsString == null || draftsString.isEmpty) {
        return [];
      }
      
      final List<dynamic> draftsJson = jsonDecode(draftsString);
      final drafts = draftsJson
          .map((json) => DraftModel.fromJson(json as Map<String, dynamic>))
          .toList();
      
      // Sort by updated time (newest first)
      drafts.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      
      return drafts;
    } catch (e) {
      if (kDebugMode) {
        print('DraftService: Error loading drafts: $e');
      }
      return [];
    }
  }

  /// Get a specific draft by ID
  static Future<DraftModel?> getDraft(String id) async {
    try {
      final drafts = await getAllDrafts();
      return drafts.firstWhere(
        (draft) => draft.id == id,
        orElse: () => throw StateError('Draft not found'),
      );
    } catch (e) {
      if (kDebugMode) {
        print('DraftService: Draft $id not found: $e');
      }
      return null;
    }
  }

  /// Delete a specific draft
  static Future<bool> deleteDraft(String id) async {
    try {
      final drafts = await getAllDrafts();
      final initialLength = drafts.length;
      
      drafts.removeWhere((draft) => draft.id == id);
      
      if (drafts.length == initialLength) {
        // No draft was removed
        return false;
      }
      
      final prefs = await SharedPreferences.getInstance();
      final draftsJson = drafts.map((d) => d.toJson()).toList();
      final success = await prefs.setString(_draftsKey, jsonEncode(draftsJson));
      
      if (kDebugMode) {
        print('DraftService: Deleted draft $id - Success: $success');
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('DraftService: Error deleting draft $id: $e');
      }
      return false;
    }
  }

  /// Delete all drafts
  static Future<bool> deleteAllDrafts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final success = await prefs.remove(_draftsKey);
      
      if (kDebugMode) {
        print('DraftService: Deleted all drafts - Success: $success');
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('DraftService: Error deleting all drafts: $e');
      }
      return false;
    }
  }

  /// Save auto-draft (temporary draft that gets overwritten)
  static Future<bool> saveAutoDraft(DraftModel draft) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final success = await prefs.setString(_autoDraftKey, jsonEncode(draft.toJson()));
      
      if (kDebugMode) {
        print('DraftService: Saved auto-draft - Success: $success');
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('DraftService: Error saving auto-draft: $e');
      }
      return false;
    }
  }

  /// Get auto-draft
  static Future<DraftModel?> getAutoDraft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final autoDraftString = prefs.getString(_autoDraftKey);
      
      if (autoDraftString == null || autoDraftString.isEmpty) {
        return null;
      }
      
      final json = jsonDecode(autoDraftString) as Map<String, dynamic>;
      return DraftModel.fromJson(json);
    } catch (e) {
      if (kDebugMode) {
        print('DraftService: Error loading auto-draft: $e');
      }
      return null;
    }
  }

  /// Clear auto-draft
  static Future<bool> clearAutoDraft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final success = await prefs.remove(_autoDraftKey);
      
      if (kDebugMode) {
        print('DraftService: Cleared auto-draft - Success: $success');
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('DraftService: Error clearing auto-draft: $e');
      }
      return false;
    }
  }

  /// Check if a file exists (for validating media paths)
  static Future<bool> fileExists(String path) async {
    try {
      final file = File(path);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Clean up drafts with invalid media files
  static Future<void> cleanupInvalidDrafts() async {
    try {
      final drafts = await getAllDrafts();
      final validDrafts = <DraftModel>[];
      
      for (final draft in drafts) {
        bool isValid = true;
        
        // Check if all image files exist
        for (final imagePath in draft.imagePaths) {
          if (!await fileExists(imagePath)) {
            isValid = false;
            break;
          }
        }
        
        // Check if all video files exist
        if (isValid) {
          for (final videoPath in draft.videoPaths) {
            if (!await fileExists(videoPath)) {
              isValid = false;
              break;
            }
          }
        }
        
        if (isValid) {
          validDrafts.add(draft);
        } else {
          if (kDebugMode) {
            print('DraftService: Removing invalid draft ${draft.id}');
          }
        }
      }
      
      // Save only valid drafts
      if (validDrafts.length != drafts.length) {
        final prefs = await SharedPreferences.getInstance();
        final draftsJson = validDrafts.map((d) => d.toJson()).toList();
        await prefs.setString(_draftsKey, jsonEncode(draftsJson));
        
        if (kDebugMode) {
          print('DraftService: Cleaned up ${drafts.length - validDrafts.length} invalid drafts');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('DraftService: Error during cleanup: $e');
      }
    }
  }

  /// Get drafts count
  static Future<int> getDraftsCount() async {
    final drafts = await getAllDrafts();
    return drafts.length;
  }

  /// Check if drafts storage is near limit
  static Future<bool> isNearLimit() async {
    final count = await getDraftsCount();
    return count >= (_maxDrafts * 0.8); // 80% of max
  }
}
