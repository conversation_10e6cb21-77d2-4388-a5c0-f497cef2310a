import 'dart:async';

/// Service for synchronizing post amount between drawer and create post screen
class PostAmountService {
  static final PostAmountService _instance = PostAmountService._internal();
  factory PostAmountService() => _instance;
  PostAmountService._internal();

  final StreamController<double> _postAmountController = StreamController<double>.broadcast();
  double _currentPostAmount = 0.05;

  /// Stream of post amount changes
  Stream<double> get postAmountStream => _postAmountController.stream;

  /// Current post amount value
  double get currentPostAmount => _currentPostAmount;

  /// Update the post amount and notify all listeners
  void updatePostAmount(double amount) {
    if (_currentPostAmount != amount) {
      _currentPostAmount = amount;
      _postAmountController.add(amount);
    }
  }

  /// Initialize with a specific amount
  void initialize(double initialAmount) {
    _currentPostAmount = initialAmount;
    _postAmountController.add(initialAmount);
  }

  /// Dispose the service
  void dispose() {
    _postAmountController.close();
  }
}
