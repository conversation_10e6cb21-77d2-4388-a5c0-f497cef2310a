import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FollowService {
  static final FollowService _instance = FollowService._internal();
  factory FollowService() => _instance;
  FollowService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String get _currentUserId => _auth.currentUser?.uid ?? '';

  /// Follow a user
  Future<bool> followUser(String targetUserId) async {
    if (_currentUserId.isEmpty || targetUserId == _currentUserId) {
      return false;
    }

    try {
      final batch = _firestore.batch();

      // Add to current user's following collection
      final followingRef = _firestore
          .collection('users')
          .doc(_currentUserId)
          .collection('following')
          .doc(targetUserId);

      batch.set(followingRef, {
        'followedAt': FieldValue.serverTimestamp(),
        'userId': targetUserId,
      });

      // Add to target user's followers collection
      final followerRef = _firestore
          .collection('users')
          .doc(targetUserId)
          .collection('followers')
          .doc(_currentUserId);

      batch.set(followerRef, {
        'followedAt': FieldValue.serverTimestamp(),
        'userId': _currentUserId,
      });

      // Update following count for current user
      final currentUserRef = _firestore.collection('users').doc(_currentUserId);
      batch.update(currentUserRef, {'followingCount': FieldValue.increment(1)});

      // Update followers count for target user
      final targetUserRef = _firestore.collection('users').doc(targetUserId);
      batch.update(targetUserRef, {'followersCount': FieldValue.increment(1)});

      await batch.commit();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Unfollow a user
  Future<bool> unfollowUser(String targetUserId) async {
    if (_currentUserId.isEmpty || targetUserId == _currentUserId) {
      return false;
    }

    try {
      final batch = _firestore.batch();

      // Remove from current user's following collection
      final followingRef = _firestore
          .collection('users')
          .doc(_currentUserId)
          .collection('following')
          .doc(targetUserId);

      batch.delete(followingRef);

      // Remove from target user's followers collection
      final followerRef = _firestore
          .collection('users')
          .doc(targetUserId)
          .collection('followers')
          .doc(_currentUserId);

      batch.delete(followerRef);

      // Update following count for current user
      final currentUserRef = _firestore.collection('users').doc(_currentUserId);
      batch.update(currentUserRef, {
        'followingCount': FieldValue.increment(-1),
      });

      // Update followers count for target user
      final targetUserRef = _firestore.collection('users').doc(targetUserId);
      batch.update(targetUserRef, {'followersCount': FieldValue.increment(-1)});

      await batch.commit();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if current user is following a specific user
  Stream<bool> isFollowingStream(String targetUserId) {
    if (_currentUserId.isEmpty || targetUserId == _currentUserId) {
      return Stream.value(false);
    }

    return _firestore
        .collection('users')
        .doc(_currentUserId)
        .collection('following')
        .doc(targetUserId)
        .snapshots()
        .map((snap) => snap.exists);
  }

  /// Get list of users that current user is following
  Stream<List<String>> getFollowingStream() {
    if (_currentUserId.isEmpty) {
      return Stream.value([]);
    }

    return _firestore
        .collection('users')
        .doc(_currentUserId)
        .collection('following')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.id).toList());
  }

  /// Get list of users following the current user
  Stream<List<String>> getFollowersStream() {
    if (_currentUserId.isEmpty) {
      return Stream.value([]);
    }

    return _firestore
        .collection('users')
        .doc(_currentUserId)
        .collection('followers')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.id).toList());
  }

  /// Get following count for a user
  Future<int> getFollowingCount(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      return userDoc.data()?['followingCount'] ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// Get followers count for a user
  Future<int> getFollowersCount(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      return userDoc.data()?['followersCount'] ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// Get suggested users to follow (users not currently followed)
  Stream<List<Map<String, dynamic>>> getSuggestedUsersStream() {
    if (_currentUserId.isEmpty) {
      return Stream.value([]);
    }

    return _firestore
        .collection('users')
        .where(FieldPath.documentId, isNotEqualTo: _currentUserId)
        .limit(20)
        .snapshots()
        .asyncMap((snapshot) async {
          final users = <Map<String, dynamic>>[];

          for (final doc in snapshot.docs) {
            final userData = doc.data();
            userData['id'] = doc.id;

            // Check if already following
            final isFollowing = await _firestore
                .collection('users')
                .doc(_currentUserId)
                .collection('following')
                .doc(doc.id)
                .get()
                .then((snap) => snap.exists);

            if (!isFollowing) {
              users.add(userData);
            }
          }

          return users;
        });
  }
}
