import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:image_picker/image_picker.dart';

class VideoThumbnailService {
  static const int thumbnailQuality = 75;
  static const int maxWidth = 300;
  static const int maxHeight = 300;

  /// Generate thumbnail from video URL (network)
  static Future<String?> generateThumbnailFromUrl(String videoUrl) async {
    try {
      if (kIsWeb) {
        // For web, use data-based thumbnail instead of file-based
        final thumbnailData = await VideoThumbnail.thumbnailData(
          video: videoUrl,
          imageFormat: ImageFormat.JPEG,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          quality: thumbnailQuality,
        );

        // Store the thumbnail data in a static cache for web
        if (thumbnailData != null) {
          final cacheKey = 'thumbnail_${DateTime.now().millisecondsSinceEpoch}';
          _webThumbnailCache[cacheKey] = thumbnailData;
          return cacheKey; // Return the cache key as the "path"
        }
        return null;
      }

      // For mobile platforms, use file-based approach
      final thumbnail = await VideoThumbnail.thumbnailFile(
        video: videoUrl,
        thumbnailPath: await _getThumbnailPath(),
        imageFormat: ImageFormat.JPEG,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        quality: thumbnailQuality,
      );
      return thumbnail;
    } catch (e) {
      debugPrint('Error generating thumbnail from URL: $e');
      return null;
    }
  }

  /// Generate thumbnail from local video file
  static Future<String?> generateThumbnailFromFile(String videoPath) async {
    try {
      final thumbnail = await VideoThumbnail.thumbnailFile(
        video: videoPath,
        thumbnailPath: await _getThumbnailPath(),
        imageFormat: ImageFormat.JPEG,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        quality: thumbnailQuality,
      );
      return thumbnail;
    } catch (e) {
      debugPrint('Error generating thumbnail from file: $e');
      return null;
    }
  }

  /// Generate thumbnail as bytes (for web or memory usage)
  static Future<Uint8List?> generateThumbnailData(String videoPath) async {
    try {
      final thumbnailData = await VideoThumbnail.thumbnailData(
        video: videoPath,
        imageFormat: ImageFormat.JPEG,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        quality: thumbnailQuality,
      );
      return thumbnailData;
    } catch (e) {
      debugPrint('Error generating thumbnail data: $e');
      return null;
    }
  }

  // Cache for web thumbnails (in-memory)
  static final Map<String, Uint8List> _webThumbnailCache = {};

  /// Get thumbnail data from cache (for web)
  static Uint8List? getWebThumbnailData(String cacheKey) {
    return _webThumbnailCache[cacheKey];
  }

  /// Check if a path is a web cache key
  static bool isWebCacheKey(String? path) {
    if (path == null) return false;
    return kIsWeb &&
        path.startsWith('thumbnail_') &&
        _webThumbnailCache.containsKey(path);
  }

  /// Get a unique thumbnail path
  static Future<String> _getThumbnailPath() async {
    if (kIsWeb) {
      // For web, return a dummy path
      return 'web_thumbnail_${DateTime.now().millisecondsSinceEpoch}.jpg';
    }

    final directory = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${directory.path}/thumbnail_$timestamp.jpg';
  }

  /// Clean up old thumbnail files
  static Future<void> cleanupThumbnails() async {
    try {
      final directory = await getTemporaryDirectory();
      final files = directory.listSync();

      for (final file in files) {
        if (file.path.contains('thumbnail_') && file.path.endsWith('.jpg')) {
          final fileStats = await file.stat();
          final age = DateTime.now().difference(fileStats.modified);

          // Delete thumbnails older than 1 hour
          if (age.inHours > 1) {
            await file.delete();
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up thumbnails: $e');
    }
  }

  /// Get video duration from file using video_player
  static Future<Duration?> getVideoDuration(String videoPath) async {
    try {
      VideoPlayerController? controller;

      if (videoPath.startsWith('http')) {
        // Network URL
        controller = VideoPlayerController.networkUrl(Uri.parse(videoPath));
      } else {
        // Local file
        controller = VideoPlayerController.file(File(videoPath));
      }

      await controller.initialize();
      final duration = controller.value.duration;
      await controller.dispose();

      return duration;
    } catch (e) {
      debugPrint('Error getting video duration: $e');
      return null;
    }
  }

  /// Get video duration from XFile (for image_picker)
  static Future<Duration?> getVideoDurationFromXFile(XFile videoFile) async {
    try {
      if (kIsWeb) {
        // For web, we can't easily get duration without uploading
        // Return null to skip validation on web
        debugPrint('Video duration validation skipped on web platform');
        return null;
      }

      // For mobile, use file path
      final controller = VideoPlayerController.file(File(videoFile.path));
      await controller.initialize();
      final duration = controller.value.duration;
      await controller.dispose();

      return duration;
    } catch (e) {
      debugPrint('Error getting video duration from XFile: $e');
      return null;
    }
  }

  /// Format duration for display
  static String formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
