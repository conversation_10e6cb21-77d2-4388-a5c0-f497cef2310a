import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

import '../../widgets/payments/payment_modal_web.dart';

/// Simple Stripe service for payment processing using Payment Sheet
class StripeService {
  // Direct Cloud Function URL
  static const String _createPaymentIntentUrl =
      'https://us-central1-money-mouthy.cloudfunctions.net/createPaymentIntent';

  /// Create payment intent on backend
  static Future<Map<String, dynamic>?> _createPaymentIntent(
    double amount,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get Firebase ID token for authentication
      final idToken = await user.getIdToken();

      final response = await http.post(
        Uri.parse(_createPaymentIntentUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $idToken',
        },
        body: jsonEncode({
          'amount': (amount * 100).round(),
          'currency': 'usd',
          'email': user.email,
        }),
      );

      if (response.statusCode != 200) {
        final errorData = jsonDecode(response.body);
        throw Exception(
          'HTTP ${response.statusCode}: ${errorData['error'] ?? 'Unknown error'}',
        );
      }

      final responseData = jsonDecode(response.body);

      // Debug: Print the actual response structure
      debugPrint('Payment Intent Response: $responseData');

      // Handle different possible response structures
      Map<String, dynamic>? result;
      if (responseData.containsKey('result') &&
          responseData['result'] != null) {
        result = responseData['result'] as Map<String, dynamic>;
      } else if (responseData is Map<String, dynamic>) {
        // If no 'result' wrapper, use the response data directly
        result = responseData;
      }

      // Validate required fields exist
      if (result == null) {
        throw Exception('Invalid response: result is null');
      }

      if (!result.containsKey('ephemeralKey') ||
          !result.containsKey('paymentIntent')) {
        throw Exception(
          'Invalid response: missing required fields. Response: $responseData',
        );
      }

      return {
        'ephemeralKey': result['ephemeralKey'],
        'paymentIntent': result['paymentIntent'],
        'customerEmail': result['customerEmail'] ?? result['customer_email'],
        'customerId': result['customer'] ?? result['customerId'],
      };
    } catch (e) {
      debugPrint('Error creating payment intent: $e');

      if (e.toString().contains('HTTP 401') ||
          e.toString().contains('Unauthorized')) {
        throw Exception('Authentication failed. Please sign in again.');
      }

      throw Exception('Failed to create payment intent: $e');
    }
  }

  /// Process payment using Stripe Payment Sheet (mobile) or Payment Element (web)
  static Future<bool> processPayment(double amount,
      [BuildContext? context]) async {
    try {
      // Create payment intent
      final paymentData = await _createPaymentIntent(amount);
      if (paymentData == null) {
        throw Exception('Failed to create payment intent, but HTTP ok');
      }

      if (kIsWeb && context != null) {
        // Web payment using Payment Element
        // Note: Context is passed across async gap but is safely used in dialog
        return await _processWebPayment(
            paymentData['paymentIntent'], amount, context);
      } else {
        // Mobile payment using Payment Sheet
        return await _processMobilePayment(paymentData);
      }
    } on StripeException catch (e) {
      debugPrint('Stripe error: ${e.error.localizedMessage}');

      // Re-throw cancellation errors so they can be handled appropriately
      if (e.error.code == FailureCode.Canceled) {
        throw Exception('Payment was cancelled');
      }

      throw Exception(e.error.localizedMessage ?? 'Payment failed');
    } catch (e) {
      debugPrint('Payment error: $e');
      rethrow;
    }
  }

  /// Process mobile payment using Payment Sheet
  static Future<bool> _processMobilePayment(
      Map<String, dynamic> paymentData) async {
    // Initialize payment sheet
    await Stripe.instance.initPaymentSheet(
      paymentSheetParameters: SetupPaymentSheetParameters(
        paymentIntentClientSecret: paymentData['paymentIntent'],
        customerEphemeralKeySecret: paymentData['ephemeralKey'],
        merchantDisplayName: 'Money Mouthy',
        customerId: paymentData['customer'],
        style: ThemeMode.system,
        billingDetails: BillingDetails(
          email: paymentData['customerEmail'],
          name: FirebaseAuth.instance.currentUser?.displayName,
        ),
      ),
    );

    // Present payment sheet
    await Stripe.instance.presentPaymentSheet();
    debugPrint('Mobile payment successful');
    return true;
  }

  /// Process web payment using Payment Modal
  static Future<bool> _processWebPayment(
      String clientSecret, double amount, BuildContext? context) async {
    if (kIsWeb && context != null) {
      // Show payment modal instead of navigating to a new screen
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => PaymentModalWeb(
          amount: amount,
          clientSecret: clientSecret,
          onSuccess: () {
            // Modal will handle its own closing
          },
          onCancel: () {
            Navigator.of(context).pop(false);
          },
        ),
      );
      return result ?? false;
    }
    return false;
  }
}

/// Web-specific payment dialog using Stripe Payment Element
// class _WebPaymentDialog extends StatefulWidget {
//   final String clientSecret;
//   final double amount;

//   const _WebPaymentDialog({
//     required this.clientSecret,
//     required this.amount,
//   });

//   @override
//   State<_WebPaymentDialog> createState() => _WebPaymentDialogState();
// }

// class _WebPaymentDialogState extends State<_WebPaymentDialog> {
//   bool _isLoading = false;

//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//       child: Container(
//         width: 500,
//         padding: EdgeInsets.all(24),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Header
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Text(
//                   'Complete Payment',
//                   style: Theme.of(context).textTheme.headlineSmall?.copyWith(
//                         fontWeight: FontWeight.bold,
//                       ),
//                 ),
//                 IconButton(
//                   onPressed: () => Navigator.of(context).pop(false),
//                   icon: Icon(Icons.close),
//                 ),
//               ],
//             ),

//             SizedBox(height: 8),

//             // Amount display
//             Container(
//               width: double.infinity,
//               padding: EdgeInsets.all(16),
//               decoration: BoxDecoration(
//                 color: Colors.green.shade50,
//                 borderRadius: BorderRadius.circular(8),
//                 border: Border.all(color: Colors.green.shade200),
//               ),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     'Amount to Add',
//                     style: TextStyle(
//                       color: Colors.green.shade700,
//                       fontWeight: FontWeight.w500,
//                     ),
//                   ),
//                   SizedBox(height: 4),
//                   Text(
//                     '\$${widget.amount.toStringAsFixed(2)}',
//                     style: TextStyle(
//                       fontSize: 24,
//                       fontWeight: FontWeight.bold,
//                       color: Colors.green.shade800,
//                     ),
//                   ),
//                 ],
//               ),
//             ),

//             SizedBox(height: 24),

//             // For web, we'll show a simple message and use Stripe's built-in payment sheet
//             Text(
//               'Payment Details',
//               style: TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.w600,
//               ),
//             ),
//             SizedBox(height: 12),

//             Container(
//               height: 100,
//               width: double.infinity,
//               decoration: BoxDecoration(
//                 border: Border.all(color: Colors.grey.shade300),
//                 borderRadius: BorderRadius.circular(8),
//               ),
//               child: Center(
//                 child: Text(
//                   'Web payment will open in a new window',
//                   style: TextStyle(
//                     color: Colors.grey.shade600,
//                     fontSize: 14,
//                   ),
//                 ),
//               ),
//             ),

//             SizedBox(height: 24),

//             // Action buttons
//             Row(
//               children: [
//                 Expanded(
//                   child: TextButton(
//                     onPressed: _isLoading
//                         ? null
//                         : () => Navigator.of(context).pop(false),
//                     child: Text('Cancel'),
//                   ),
//                 ),
//                 SizedBox(width: 12),
//                 Expanded(
//                   flex: 2,
//                   child: ElevatedButton(
//                     onPressed: _isLoading ? null : _processPayment,
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: Colors.green.shade600,
//                       foregroundColor: Colors.white,
//                       padding: EdgeInsets.symmetric(vertical: 16),
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(8),
//                       ),
//                     ),
//                     child: _isLoading
//                         ? SizedBox(
//                             height: 20,
//                             width: 20,
//                             child: CircularProgressIndicator(
//                               strokeWidth: 2,
//                               valueColor:
//                                   AlwaysStoppedAnimation<Color>(Colors.white),
//                             ),
//                           )
//                         : Text(
//                             'Pay \$${widget.amount.toStringAsFixed(2)}',
//                             style: TextStyle(
//                               fontSize: 16,
//                               fontWeight: FontWeight.w600,
//                             ),
//                           ),
//                   ),
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Future<void> _processPayment() async {
//     setState(() {
//       _isLoading = true;
//     });

//     try {
//       // For web, we'll use the standard Stripe payment sheet
//       // This is a simplified implementation - in production you might want to use
//       // the Payment Element for better web experience
//       if (mounted) {
//         Navigator.of(context).pop(true);
//       }
//     } catch (e) {
//       debugPrint('Web payment error: $e');

//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('Payment failed: ${e.toString()}'),
//             backgroundColor: Colors.red,
//           ),
//         );
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }
// }
