// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:money_mouthy_two/screens/profile_screen.dart';

// import '../widgets/home/<USER>';

// /// HomeScreen - Wrapper for HomeContent with bottom navigation
// class HomeScreen extends StatelessWidget {
//   const HomeScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Center(
//         child: Container(
//           constraints: BoxConstraints(maxWidth: kIsWeb ? 800 : double.infinity),
//           child: const HomeContent(),
//         ),
//       ),
//       bottomNavigationBar: HomeBottomNavigationBar(),
//     );
//   }
// }

// /// Bottom Navigation Bar for HomeScreen (when used standalone)
// class HomeBottomNavigationBar extends StatelessWidget {
//   const HomeBottomNavigationBar({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return BottomNavigationBar(
//       type: BottomNavigationBarType.fixed,
//       selectedItemColor: const Color(0xFF4C5DFF),
//       unselectedItemColor: Colors.grey,
//       currentIndex: 0,
//       items: const [
//         BottomNavigationBarItem(icon: Icon(Icons.home), label: ''),
//         BottomNavigationBarItem(
//           icon: Icon(Icons.chat_bubble_outline),
//           label: '',
//         ),
//         BottomNavigationBarItem(icon: Icon(Icons.add_box_outlined), label: ''),
//         BottomNavigationBarItem(icon: Icon(Icons.search), label: ''),
//         BottomNavigationBarItem(icon: Icon(Icons.person_outline), label: ''),
//       ],
//       onTap: (index) => _handleBottomNavTap(context, index),
//     );
//   }

//   void _handleBottomNavTap(BuildContext context, int index) {
//     switch (index) {
//       case 1:
//         Navigator.pushNamed(context, '/chats');
//         break;
//       case 2:
//         Navigator.pushNamed(context, '/create_post');
//         break;
//       case 3:
//         Navigator.pushNamed(context, '/search');
//         break;
//       case 4:
//         final uid = FirebaseAuth.instance.currentUser?.uid;
//         if (uid != null) {
//           Navigator.push(
//             context,
//             MaterialPageRoute(builder: (_) => ProfileScreen(userId: uid)),
//           );
//         }
//         break;
//     }
//   }
// }
