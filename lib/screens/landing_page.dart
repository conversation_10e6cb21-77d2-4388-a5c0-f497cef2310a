import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import 'package:easy_localization/easy_localization.dart';

class LandingPage extends StatefulWidget {
  const LandingPage({super.key});

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> {
  late YoutubePlayerController _youtubeController;
  bool _hasAutoDetected = false;

  @override
  void initState() {
    super.initState();
    _youtubeController = YoutubePlayerController.fromVideoId(
      videoId: 'H9UEXTKkQ34',
      autoPlay: false,
      params: const YoutubePlayerParams(
        mute: false,
        showControls: true,
        showFullscreenButton: true,
        enableCaption: true,
        captionLanguage: 'en',
      ),
    );

    // Auto-detect browser language for web
    if (kIsWeb && !_hasAutoDetected) {
      _autoDetectLanguage();
    }
  }

  void _autoDetectLanguage() {
    if (!kIsWeb) return;

    try {
      // Get browser language
      final browserLanguage = context.deviceLocale.languageCode;
      final supportedLanguages = ['en', 'es', 'fr', 'de', 'zh', 'ja', 'ar'];

      if (supportedLanguages.contains(browserLanguage)) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.setLocale(Locale(browserLanguage)).then((_) {
            setState(() {
              _hasAutoDetected = true;
            });
          });
        });
      }
    } catch (e) {
      debugPrint('Auto-detection failed: $e');
    }
  }

  @override
  void dispose() {
    _youtubeController.close();
    super.dispose();
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    final isLoggedIn = user != null && user.emailVerified;

    // For web, wrap the entire scaffold in a widget that rebuilds on locale changes
    if (kIsWeb) {
      return Builder(
        key: ValueKey(context.locale.languageCode),
        builder: (context) => _buildScaffold(context, isLoggedIn),
      );
    }

    return _buildScaffold(context, isLoggedIn);
  }

  Widget _buildScaffold(BuildContext context, bool isLoggedIn) {
    return Scaffold(
      backgroundColor: const Color(0xFF0f172a),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Hero Section
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: const [
                    Color.fromARGB(255, 129, 56, 123),
                    Color.fromARGB(255, 34, 57, 88),
                    Color.fromARGB(255, 107, 94, 182),
                  ],
                  transform: GradientRotation(
                    (DateTime.now().millisecondsSinceEpoch / 3000) %
                        (2 * 3.14159),
                  ),
                ),
              ),
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 80,
                  ),
                  child: Column(
                    children: [
                      // Language Selector (Web only)
                      if (kIsWeb) ...[
                        Align(
                          alignment: Alignment.topRight,
                          child: _buildLanguageSelector(),
                        ),
                        const SizedBox(height: 20),
                      ],
                      // Logo and Title
                      Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color.fromARGB(255, 177, 175, 175),
                          ),
                          child: ClipOval(
                            child: Image.asset(
                              'assets/images/money_mouth.png',
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 32),
                      Text(
                        'Money Mouthy',
                        style: const TextStyle(
                          fontSize: 56,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: -1,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        kIsWeb
                            ? _getTranslatedText('tagline')
                            : 'Put Your Money Where Your Mouth Is',
                        style: const TextStyle(
                          fontSize: 24,
                          color: Colors.white70,
                          fontWeight: FontWeight.w300,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF5159FF).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(50),
                          border: Border.all(
                            color: const Color(
                              0xFF5159FF,
                            ).withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          kIsWeb
                              ? _getTranslatedText('sub_tagline')
                              : '💰 Your opinion has minimum value - \$0.05 more! 💰',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 48),

                      // Action Buttons
                      if (isLoggedIn) ...[
                        _buildPrimaryButton(
                          kIsWeb
                              ? _getTranslatedText('go_to_home')
                              : 'Go to Home',
                          Icons.home,
                          () =>
                              Navigator.pushReplacementNamed(context, '/home'),
                        ),
                      ] else ...[
                        Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          alignment: WrapAlignment.center,
                          children: [
                            _buildPrimaryButton(
                              kIsWeb
                                  ? _getTranslatedText('get_started')
                                  : 'Get Started',
                              Icons.rocket_launch,
                              () => Navigator.pushNamed(context, '/signup'),
                            ),
                            _buildSecondaryButton(
                              kIsWeb
                                  ? _getTranslatedText('sign_in')
                                  : 'Sign In',
                              Icons.login,
                              () => Navigator.pushNamed(context, '/login'),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),

            // Features Section
            Container(
              width: double.infinity,
              color: Colors.white,
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 40,
                  ),
                  child: Column(
                    children: [
                      // Download Buttons (moved from bottom)
                      Wrap(
                        spacing: 16,
                        runSpacing: 16,
                        alignment: WrapAlignment.center,
                        children: [
                          _buildOutlinedDownloadButton(
                            kIsWeb
                                ? _getTranslatedText('download_android')
                                : 'Download for Android',
                            Icons.android,
                            () => _launchURL(
                              '/releases/moneymouthy-android.apk',
                            ),
                          ),
                          _buildOutlinedDownloadButton(
                            kIsWeb
                                ? _getTranslatedText('download_ios')
                                : 'Download for iOS',
                            Icons.apple,
                            () => _launchURL('/downloads/moneymouthy-ios.ipa'),
                          ),
                        ],
                      ),

                      // YouTube Video Section
                      const SizedBox(height: 32),

                      _buildYouTubeVideoSection(),
                      // const SizedBox(height: 32),
                      Text(
                        kIsWeb
                            ? _getTranslatedText('why_money_mouthy')
                            : 'Why Money Mouthy?',
                        style: const TextStyle(
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0f172a),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 48),
                      Wrap(
                        spacing: 32,
                        runSpacing: 32,
                        alignment: WrapAlignment.center,
                        children: [
                          _buildFeatureCard(
                            Icons.monetization_on,
                            kIsWeb
                                ? _getTranslatedText('earn_money')
                                : 'Earn Money',
                            kIsWeb
                                ? _getTranslatedText('earn_money_desc')
                                : 'Get paid for your opinions and posts',
                          ),
                          _buildFeatureCard(
                            Icons.people,
                            kIsWeb ? _getTranslatedText('connect') : 'Connect',
                            kIsWeb
                                ? _getTranslatedText('connect_desc')
                                : 'Join a community of like-minded people',
                          ),
                          _buildFeatureCard(
                            Icons.trending_up,
                            kIsWeb ? _getTranslatedText('grow') : 'Grow',
                            kIsWeb
                                ? _getTranslatedText('grow_desc')
                                : 'Build your following and influence',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Navigation Links Section
            Container(
              width: double.infinity,
              color: const Color(0xFF0f172a),
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 40,
                  ),
                  child: Wrap(
                    spacing: 24,
                    runSpacing: 16,
                    alignment: WrapAlignment.center,
                    children: [
                      TextButton(
                        onPressed: () =>
                            Navigator.pushNamed(context, '/privacy'),
                        child: Text(
                          kIsWeb
                              ? _getTranslatedText('privacy_policy')
                              : 'Privacy Policy',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pushNamed(context, '/terms'),
                        child: Text(
                          kIsWeb
                              ? _getTranslatedText('terms_of_service')
                              : 'Terms of Service',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pushNamed(context, '/about'),
                        child: Text(
                          kIsWeb ? _getTranslatedText('about_us') : 'About Us',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ),
                      TextButton(
                        onPressed: () =>
                            Navigator.pushNamed(context, '/support'),
                        child: Text(
                          kIsWeb ? _getTranslatedText('support') : 'Support',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Footer
            Container(
              width: double.infinity,
              color: const Color(0xFF0f172a),
              padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 24),
              child: Center(
                child: Text(
                  kIsWeb
                      ? _getTranslatedText('copyright')
                      : '© 2025 Money Mouthy. All rights reserved.',
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrimaryButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF5159FF),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        elevation: 0,
      ),
    );
  }

  Widget _buildSecondaryButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        side: const BorderSide(color: Colors.white, width: 2),
      ),
    );
  }

  Widget _buildFeatureCard(IconData icon, String title, String description) {
    return Container(
      width: 300,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF5159FF).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, size: 32, color: const Color(0xFF5159FF)),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF0f172a),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: const TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOutlinedDownloadButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.black,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        side: const BorderSide(color: Colors.black, width: 1),
      ),
    );
  }

  Widget _buildYouTubeVideoSection() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 600),
      child: Column(
        children: [
          Text(
            kIsWeb
                ? _getTranslatedText('why_money_mouthy')
                : 'Why Money Mouthy?',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF0f172a),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: YoutubePlayer(
                controller: _youtubeController,
                aspectRatio: 16 / 9,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            kIsWeb
                ? _getTranslatedText('learn_how_works')
                : 'Learn how Money Mouthy works',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector() {
    if (!kIsWeb) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<Locale>(
          value: context.locale,
          icon: const Icon(Icons.language, color: Colors.white, size: 20),
          dropdownColor: const Color(0xFF0f172a),
          style: const TextStyle(color: Colors.white),
          items: [
            DropdownMenuItem(
              value: context.deviceLocale,
              child:
                  const Text('🌐 Auto', style: TextStyle(color: Colors.white)),
            ),
            const DropdownMenuItem(
              value: Locale('en'),
              child:
                  Text('🇺🇸 English', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('es'),
              child:
                  Text('🇪🇸 Español', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('fr'),
              child:
                  Text('🇫🇷 Français', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('de'),
              child:
                  Text('🇩🇪 Deutsch', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('zh'),
              child: Text('🇨🇳 中文', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('ja'),
              child: Text('🇯🇵 日本語', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('ar'),
              child:
                  Text('🇸🇦 العربية', style: TextStyle(color: Colors.white)),
            ),
          ],
          onChanged: (Locale? locale) {
            if (locale != null) {
              debugPrint('Changing locale to: ${locale.languageCode}');
              if (locale == context.deviceLocale) {
                // Auto-detect language
                _autoDetectLanguage();
              } else {
                context.setLocale(locale).then((_) {
                  if (mounted) {
                    debugPrint(
                        'Locale changed successfully to: ${locale.languageCode}');
                    // Force rebuild after locale change
                    setState(() {});
                  }
                });
              }
            }
          },
        ),
      ),
    );
  }

  String _getTranslatedText(String key) {
    final currentLocale = context.locale.languageCode;
    debugPrint('Getting translation for $key in locale: $currentLocale');

    final translations = {
      'tagline': {
        'en': 'Put Your Money Where Your Mouth Is',
        'es': 'Pon Tu Dinero Donde Está Tu Boca',
        'fr': 'Mettez Votre Argent Là Où Est Votre Bouche',
        'de': 'Setzen Sie Ihr Geld Dort Ein, Wo Ihr Mund Ist',
        'zh': '把你的钱放在你的嘴上',
        'ja': 'お金を口に出すところに置く',
        'ar': 'ضع أموالك حيث فمك',
      },
      'get_started': {
        'en': 'Get Started',
        'es': 'Comenzar',
        'fr': 'Commencer',
        'de': 'Loslegen',
        'zh': '开始使用',
        'ja': '始める',
        'ar': 'ابدأ',
      },
      'sign_in': {
        'en': 'Sign In',
        'es': 'Iniciar Sesión',
        'fr': 'Se Connecter',
        'de': 'Anmelden',
        'zh': '登录',
        'ja': 'サインイン',
        'ar': 'تسجيل الدخول',
      },
      'go_to_home': {
        'en': 'Go to Home',
        'es': 'Ir al Inicio',
        'fr': 'Aller à l\'Accueil',
        'de': 'Zur Startseite',
        'zh': '回到首页',
        'ja': 'ホームに戻る',
        'ar': 'العودة للرئيسية',
      },
      'download_android': {
        'en': 'Download for Android',
        'es': 'Descargar para Android',
        'fr': 'Télécharger pour Android',
        'de': 'Für Android Herunterladen',
        'zh': '下载安卓版',
        'ja': 'Android版をダウンロード',
        'ar': 'تحميل لأندرويد',
      },
      'download_ios': {
        'en': 'Download for iOS',
        'es': 'Descargar para iOS',
        'fr': 'Télécharger pour iOS',
        'de': 'Für iOS Herunterladen',
        'zh': '下载iOS版',
        'ja': 'iOS版をダウンロード',
        'ar': 'تحميل لآيفون',
      },
      'why_money_mouthy': {
        'en': 'Why Money Mouthy?',
        'es': '¿Por Qué Money Mouthy?',
        'fr': 'Pourquoi Money Mouthy?',
        'de': 'Warum Money Mouthy?',
        'zh': '为什么选择Money Mouthy？',
        'ja': 'なぜMoney Mouthy？',
        'ar': 'لماذا Money Mouthy؟',
      },
      'earn_money': {
        'en': 'Earn Money',
        'es': 'Ganar Dinero',
        'fr': 'Gagner de l\'Argent',
        'de': 'Geld Verdienen',
        'zh': '赚钱',
        'ja': 'お金を稼ぐ',
        'ar': 'اكسب المال',
      },
      'earn_money_desc': {
        'en': 'Get paid for your opinions and posts',
        'es': 'Recibe pagos por tus opiniones y publicaciones',
        'fr': 'Soyez payé pour vos opinions et publications',
        'de': 'Werden Sie für Ihre Meinungen und Beiträge bezahlt',
        'zh': '通过您的观点和帖子获得报酬',
        'ja': 'あなたの意見と投稿で報酬を得る',
        'ar': 'احصل على أموال مقابل آرائك ومنشوراتك',
      },
      'connect': {
        'en': 'Connect',
        'es': 'Conectar',
        'fr': 'Se Connecter',
        'de': 'Verbinden',
        'zh': '连接',
        'ja': 'つながる',
        'ar': 'تواصل',
      },
      'connect_desc': {
        'en': 'Join a community of like-minded people',
        'es': 'Únete a una comunidad de personas afines',
        'fr':
            'Rejoignez une communauté de personnes partageant les mêmes idées',
        'de': 'Treten Sie einer Gemeinschaft gleichgesinnter Menschen bei',
        'zh': '加入志同道合的人群',
        'ja': '同じ考えを持つ人々のコミュニティに参加',
        'ar': 'انضم إلى مجتمع من الأشخاص ذوي التفكير المماثل',
      },
      'grow': {
        'en': 'Grow',
        'es': 'Crecer',
        'fr': 'Grandir',
        'de': 'Wachsen',
        'zh': '成长',
        'ja': '成長する',
        'ar': 'اكبر',
      },
      'grow_desc': {
        'en': 'Build your following and influence',
        'es': 'Construye tu seguimiento e influencia',
        'fr': 'Développez votre audience et votre influence',
        'de': 'Bauen Sie Ihre Anhängerschaft und Ihren Einfluss auf',
        'zh': '建立您的关注者和影响力',
        'ja': 'フォロワーと影響力を築く',
        'ar': 'ابن متابعيك وتأثيرك',
      },
      'privacy_policy': {
        'en': 'Privacy Policy',
        'es': 'Política de Privacidad',
        'fr': 'Politique de Confidentialité',
        'de': 'Datenschutzrichtlinie',
        'zh': '隐私政策',
        'ja': 'プライバシーポリシー',
        'ar': 'سياسة الخصوصية',
      },
      'terms_of_service': {
        'en': 'Terms of Service',
        'es': 'Términos de Servicio',
        'fr': 'Conditions de Service',
        'de': 'Nutzungsbedingungen',
        'zh': '服务条款',
        'ja': '利用規約',
        'ar': 'شروط الخدمة',
      },
      'about_us': {
        'en': 'About Us',
        'es': 'Acerca de Nosotros',
        'fr': 'À Propos de Nous',
        'de': 'Über Uns',
        'zh': '关于我们',
        'ja': '私たちについて',
        'ar': 'من نحن',
      },
      'support': {
        'en': 'Support',
        'es': 'Soporte',
        'fr': 'Support',
        'de': 'Support',
        'zh': '支持',
        'ja': 'サポート',
        'ar': 'الدعم',
      },
      'learn_how_works': {
        'en': 'Learn how Money Mouthy works',
        'es': 'Aprende cómo funciona Money Mouthy',
        'fr': 'Découvrez comment fonctionne Money Mouthy',
        'de': 'Erfahren Sie, wie Money Mouthy funktioniert',
        'zh': '了解Money Mouthy的工作原理',
        'ja': 'Money Mouthyの仕組みを学ぶ',
        'ar': 'تعلم كيف يعمل Money Mouthy',
      },
      'copyright': {
        'en': '© 2025 Money Mouthy. All rights reserved.',
        'es': '© 2025 Money Mouthy. Todos los derechos reservados.',
        'fr': '© 2025 Money Mouthy. Tous droits réservés.',
        'de': '© 2025 Money Mouthy. Alle Rechte vorbehalten.',
        'zh': '© 2025 Money Mouthy. 版权所有。',
        'ja': '© 2025 Money Mouthy. 全著作権所有。',
        'ar': '© 2025 Money Mouthy. جميع الحقوق محفوظة.',
      },
      'sub_tagline': {
        'en': '💰 Your opinion has minimum value - \$0.05 more! 💰',
        'es': '💰 Tu opinión tiene un valor mínimo de - \$0.05 más! 💰',
        'fr': '💰 Votre opinion a une valeur minimale de - \$0.05 plus! 💰',
        'de': '💰 Ihre Meinung ist mindestens wert - \$0.05 mehr! 💰',
        'zh': '💰 你的观点价值最低 - \$0.05更多！ 💰',
        'ja': '💰 あなたの意見は最低値 - \$0.05以上の価値があります！ 💰',
        'ar': '💰 أموالك تبلغ قيمتها الحد الأدنى - \$0.05 أكمل! 💰',
      },
    };

    return translations[key]?[currentLocale] ?? translations[key]?['en'] ?? key;
  }
}
