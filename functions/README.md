# Firebase Functions - Stripe Payment Backend

This directory contains Firebase Functions that serve as the backend for Stripe payment processing in the Money Mouthy app.

## 🚀 Quick Setup

### 1. Install Dependencies

```bash
cd functions
npm install
```

### 2. Configure Stripe Keys

Set your Stripe configuration using Firebase CLI:

```bash
# Set Stripe secret key (replace with your actual key)
firebase functions:config:set stripe.secret_key="sk_test_your_stripe_secret_key_here"

# Set Stripe publishable key (replace with your actual key)
firebase functions:config:set stripe.publishable_key="pk_test_your_stripe_publishable_key_here"

# Set webhook secret (get this from Stripe Dashboard > Webhooks)
firebase functions:config:set stripe.webhook_secret="whsec_your_webhook_secret_here"
```

### 3. Deploy Functions

```bash
# Deploy all functions
firebase deploy --only functions

# Or deploy specific function
firebase deploy --only functions:createPaymentIntent
```

## 🧪 Local Development

### 1. Start Firebase Emulators

```bash
# From project root
firebase emulators:start
```

### 2. Set Local Config

```bash
# Create local config file
cd functions
echo '{
  "stripe": {
    "secret_key": "sk_test_your_stripe_secret_key_here",
    "publishable_key": "_your_stripe_publishable_key_herpk_teste",
    "webhook_secret": "whsec_your_webhook_secret_here"
  }
}' > .runtimeconfig.json
```

### 3. Test Functions

The emulator will run on:

- Functions: http://localhost:5001
- Firestore: http://localhost:8080
- UI: http://localhost:4000

## 📡 Available Endpoints

### 1. Create Payment Intent

**POST** `/createPaymentIntent`

Creates a Stripe payment intent for processing payments.

**Request Body:**

```json
{
  "amount": 2500,
  "currency": "usd",
  "metadata": {
    "userId": "user123",
    "description": "Wallet funding"
  }
}
```

**Response:**

```json
{
  "id": "pi_1234567890",
  "client_secret": "pi_1234567890_secret_abc123",
  "amount": 2500,
  "currency": "usd",
  "status": "requires_payment_method"
}
```

### 2. Stripe Webhook

**POST** `/stripeWebhook`

Handles Stripe webhook events for payment confirmations.

### 3. Health Check

**GET** `/healthCheck`

Returns service health status.

### 4. Get Stripe Config

**GET** `/getStripeConfig`

Returns Stripe publishable key for client configuration.

## 🔒 Security Features

- ✅ CORS enabled for cross-origin requests
- ✅ Request validation and sanitization
- ✅ Webhook signature verification
- ✅ Amount limits ($0.50 - $999,999)
- ✅ Error handling and logging
- ✅ Firestore logging for audit trail

## 📊 Monitoring

### Logs

View function logs:

```bash
firebase functions:log
```

### Firestore Collections

- `payment_logs` - All payment attempts and results
- `wallets` - User wallet balances (managed by app)
- `transactions` - Transaction history (managed by app)

## 🔧 Configuration

### Environment Variables

Set via Firebase CLI:

```bash
firebase functions:config:set key.subkey="value"
```

### Required Config

- `stripe.secret_key` - Stripe secret key
- `stripe.publishable_key` - Stripe publishable key
- `stripe.webhook_secret` - Stripe webhook endpoint secret

## 🚨 Production Checklist

- [ ] Replace test Stripe keys with live keys
- [ ] Set up Stripe webhook endpoint in dashboard
- [ ] Configure proper CORS origins
- [ ] Set up monitoring and alerting
- [ ] Test with real payment methods
- [ ] Enable Firestore security rules
- [ ] Set up backup and recovery

## 📞 Support

For issues:

1. Check Firebase Functions logs
2. Verify Stripe configuration
3. Test with Stripe test cards
4. Check network connectivity

## 🔗 Useful Links

- [Firebase Functions Documentation](https://firebase.google.com/docs/functions)
- [Stripe API Documentation](https://stripe.com/docs/api)
- [Stripe Test Cards](https://stripe.com/docs/testing#cards)
