{"flutter": {"platforms": {"android": {"default": {"projectId": "money-mouthy", "appId": "1:717677160958:android:f5bc9db3c362da16a738af", "fileOutput": "android/app/google-services.json"}}, "dart": {"lib/firebase_options.dart": {"projectId": "money-mouthy", "configurations": {"android": "1:717677160958:android:f5bc9db3c362da16a738af", "ios": "1:717677160958:ios:be9f11f341b97df8a738af", "macos": "1:717677160958:ios:be9f11f341b97df8a738af", "web": "1:717677160958:web:09db1e2bfc9bdee1a738af", "windows": "1:717677160958:web:b8697cb253492500a738af"}}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}