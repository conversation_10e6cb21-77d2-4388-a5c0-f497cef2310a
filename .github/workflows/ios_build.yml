name: Build iOS IPA
on:
  workflow_dispatch:

jobs:
  build-ios:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          channel: "stable"
      - run: flutter pub get
      - run: pod repo update
        working-directory: ios
      - run: flutter build ios --release --no-codesign -v
      - run: |
          mkdir -p build/ios/iphoneos/Payload
          cp -R build/ios/iphoneos/Runner.app build/ios/iphoneos/Payload/
      - run: |
          cd build/ios/iphoneos
          zip -qr app.ipa Payload
      - uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }} # This token is provided by Actions, you do not need to create your own token
          file: build/ios/iphoneos/app.ipa
          tag: ios-build # This should be the tag you want to create or update, or the release ID if you want to update an existing release
          overwrite: true # This will overwrite the existing release if it exists
