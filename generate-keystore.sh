#!/bin/bash

# Replace these variables with your desired values
KEY_ALIAS="moneymouthy"
KEY_PASSWORD="moneymouthy"
STORE_PASSWORD="moneymouthy"
KEYSTORE_PATH="android/app/upload-keystore.jks"

# Create the keystore
keytool -genkey -v \
        -keystore $KEYSTORE_PATH \
        -alias $KEY_ALIAS \
        -keyalg RSA \
        -keysize 2048 \
        -validity 10000 \
        -storepass $STORE_PASSWORD \
        -keypass $KEY_PASSWORD \
        -dname "CN=<PERSON>, OU=Money Mouthy, O=Cuptoopia.com Inc, L=Ohio, S=Ohio, C=US"
        
echo "Keystore generated at $KEYSTORE_PATH"
echo "Make sure to update your key.properties file with these values:"
echo "storePassword=$STORE_PASSWORD"
echo "keyPassword=$KEY_PASSWORD"
echo "keyAlias=$KEY_ALIAS"
echo "storeFile=../app/upload-keystore.jks"